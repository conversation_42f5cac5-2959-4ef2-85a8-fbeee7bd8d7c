import request from '@/utils/request'

// 查询用工信息列表
export function listEmploymentInfo(query) {
  return request({
    url: '/place/employment/list',
    method: 'get',
    params: query
  })
}

// 查询用工信息详细
export function getEmploymentInfo(employmentId) {
  return request({
    url: '/place/employment/' + employmentId,
    method: 'get'
  })
}

// 查询用工信息详细信息（包含关联信息）
export function getEmploymentInfoDetail(employmentId) {
  return request({
    url: '/place/employment/detail/' + employmentId,
    method: 'get'
  })
}

// 新增用工信息
export function addEmploymentInfo(data) {
  return request({
    url: '/place/employment',
    method: 'post',
    data: data
  })
}

// 修改用工信息
export function updateEmploymentInfo(data) {
  return request({
    url: '/place/employment',
    method: 'put',
    data: data
  })
}

// 删除用工信息
export function delEmploymentInfo(employmentId) {
  return request({
    url: '/place/employment/' + employmentId,
    method: 'delete'
  })
}

// 查询已发布的用工信息列表
export function listPublishedEmploymentInfo(query) {
  return request({
    url: '/place/employment/published',
    method: 'get',
    params: query
  })
}

// 查询推荐用工信息列表
export function listFeaturedEmploymentInfo(query) {
  return request({
    url: '/place/employment/featured',
    method: 'get',
    params: query
  })
}

// 查询我发布的用工信息列表
export function listMyEmploymentInfo(query) {
  return request({
    url: '/place/employment/my',
    method: 'get',
    params: query
  })
}

// 根据用工类型查询用工信息列表
export function getEmploymentInfoByType(employmentType) {
  return request({
    url: '/place/employment/type/' + employmentType,
    method: 'get'
  })
}

// 根据工作类别查询用工信息列表
export function getEmploymentInfoByCategory(workCategory) {
  return request({
    url: '/place/employment/category/' + workCategory,
    method: 'get'
  })
}

// 根据区域代码查询用工信息列表
export function getEmploymentInfoByRegion(regionCode) {
  return request({
    url: '/place/employment/region/' + regionCode,
    method: 'get'
  })
}

// 根据薪资类型查询用工信息列表
export function getEmploymentInfoBySalaryType(salaryType) {
  return request({
    url: '/place/employment/salary-type/' + salaryType,
    method: 'get'
  })
}

// 根据薪资范围查询用工信息列表
export function getEmploymentInfoBySalaryRange(minSalary, maxSalary) {
  return request({
    url: '/place/employment/salary-range',
    method: 'get',
    params: { minSalary, maxSalary }
  })
}

// 根据紧急程度查询用工信息列表
export function getEmploymentInfoByUrgency(urgencyLevel) {
  return request({
    url: '/place/employment/urgency/' + urgencyLevel,
    method: 'get'
  })
}

// 查询用工信息统计信息
export function getEmploymentInfoStatistics() {
  return request({
    url: '/place/employment/statistics',
    method: 'get'
  })
}

// 根据关键词搜索用工信息
export function searchEmploymentInfo(keyword) {
  return request({
    url: '/place/employment/search',
    method: 'get',
    params: { keyword }
  })
}

// 查询即将到期的用工信息
export function getEmploymentInfoExpiringSoon(days) {
  return request({
    url: '/place/employment/expiring',
    method: 'get',
    params: { days }
  })
}

// 查询相似的用工信息列表
export function getSimilarEmploymentInfo(employmentId, limit) {
  return request({
    url: '/place/employment/' + employmentId + '/similar',
    method: 'get',
    params: { limit }
  })
}

// 更新用工信息申请次数
export function updateEmploymentApplicationCount(employmentId) {
  return request({
    url: '/place/employment/' + employmentId + '/apply',
    method: 'post'
  })
}

// 获取所有用工类型列表
export function getAllEmploymentTypes() {
  return request({
    url: '/place/employment/types',
    method: 'get'
  })
}

// 获取所有工作类别列表
export function getAllWorkCategories() {
  return request({
    url: '/place/employment/categories',
    method: 'get'
  })
}

// 获取所有薪资类型列表
export function getAllSalaryTypes() {
  return request({
    url: '/place/employment/salary-types',
    method: 'get'
  })
}

// 获取所有区域列表
export function getAllEmploymentRegions() {
  return request({
    url: '/place/employment/regions',
    method: 'get'
  })
}

// 获取所有学历要求列表
export function getAllEducationRequirements() {
  return request({
    url: '/place/employment/educations',
    method: 'get'
  })
}

// 根据核心字段搜索用工信息
export function searchEmploymentInfoByCoreFields(employmentType, workCategory, salaryType, regionCode, keyword) {
  return request({
    url: '/place/employment/core-search',
    method: 'get',
    params: { employmentType, workCategory, salaryType, regionCode, keyword }
  })
}
