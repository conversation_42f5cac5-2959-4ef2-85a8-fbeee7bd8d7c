export const createJobPostingTableOption = (proxy) => {

    // 工作类型选项
    const jobTypeOptions = [
        { label: "全职", value: "全职" },
        { label: "兼职", value: "兼职" },
        { label: "临时工", value: "临时工" },
        { label: "小时工", value: "小时工" },
        { label: "实习", value: "实习" }
    ];

    // 工作类别选项
    const jobCategoryOptions = [
        { label: "服务员", value: "服务员" },
        { label: "保洁", value: "保洁" },
        { label: "搬运工", value: "搬运工" },
        { label: "销售", value: "销售" },
        { label: "客服", value: "客服" },
        { label: "配送员", value: "配送员" },
        { label: "厨师", value: "厨师" },
        { label: "司机", value: "司机" },
        { label: "保安", value: "保安" },
        { label: "其他", value: "其他" }
    ];

    // 薪资类型选项
    const salaryTypeOptions = [
        { label: "小时", value: "hourly" },
        { label: "日薪", value: "daily" },
        { label: "月薪", value: "monthly" },
        { label: "计件", value: "piece" }
    ];



    // 状态选项
    const statusOptions = [
        { label: "草稿", value: "draft" },
        { label: "已发布", value: "published" },
        { label: "已暂停", value: "paused" },
        { label: "已关闭", value: "closed" },
        { label: "已完成", value: "completed" }
    ];

    return {
        dialogWidth: '900px',
        dialogHeight: '70vh',
        labelWidth: '100px',
        column: [
            // ==================== 核心匹配字段（必填，用于匹配） ====================
            {
                label: "职位名称",
                prop: "jobTitle",
                search: true,
                searchSpan: 8,
                minWidth: 200,
                rules: [
                    { required: true, message: "职位名称不能为空", trigger: "blur" },
                    { min: 2, max: 100, message: "职位名称长度必须介于 2 和 100 之间", trigger: "blur" }
                ],
                span: 8
            },
            {
                label: "工作类型",
                prop: "jobType",
                search: true,
                searchSpan: 6,
                width: 100,
                align: "center",
                type: "select",
                dicData: jobTypeOptions,
                span: 8,
                slot: true,
                rules: [
                    { required: true, message: "工作类型不能为空", trigger: "change" }
                ]
            },
            {
                label: "工作类别",
                prop: "jobCategory",
                search: true,
                searchSpan: 6,
                width: 100,
                align: "center",
                type: "select",
                dicData: jobCategoryOptions,
                span: 8,
                slot: true,
                rules: [
                    { required: true, message: "工作类别不能为空", trigger: "change" }
                ]
            },
            {
                label: "薪资类型",
                prop: "salaryType",
                search: true,
                searchSpan: 6,
                width: 80,
                align: "center",
                type: "select",
                dicData: salaryTypeOptions,
                span: 8,
                rules: [
                    { required: true, message: "薪资类型不能为空", trigger: "change" }
                ]
            },
            {
                label: "学历要求",
                prop: "educationRequired",
                search: true,
                searchSpan: 6,
                width: 80,
                align: "center",
                type: "select",
                dicData: [
                    { label: "不限", value: "不限" },
                    { label: "初中", value: "初中" },
                    { label: "高中", value: "高中" },
                    { label: "中专", value: "中专" },
                    { label: "大专", value: "大专" },
                    { label: "本科", value: "本科" }
                ],
                span: 8,
                rules: [
                    { required: true, message: "学历要求不能为空", trigger: "change" }
                ]
            },
            // ==================== 基础信息 ====================
            {
                label: "工作地点",
                prop: "workLocation",
                search: true,
                searchSpan: 8,
                minWidth: 120,
                span: 8,
                rules: [
                    { required: true, message: "工作地点不能为空", trigger: "blur" },
                    { max: 100, message: "工作地点不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "最低薪资",
                prop: "salaryMin",
                search: false,
                type: "number",
                span: 8,
                precision: 0,
                min: 0,
                rules: [
                    { required: true, message: "最低薪资不能为空", trigger: "blur" },
                    { type: 'number', min: 0, message: '最低薪资不能小于0', trigger: 'blur' }
                ]
            },
            {
                label: "最高薪资",
                prop: "salaryMax",
                search: false,
                type: "number",
                span: 8,
                precision: 0,
                min: 0,
                rules: [
                    { type: 'number', min: 0, message: '最高薪资不能小于0', trigger: 'blur' }
                ]
            },
            {
                label: "职位描述",
                prop: "jobDescription",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 3,
                maxRows: 6,
                showWordLimit: true,
                maxlength: 500,
                rules: [
                    { required: true, message: "职位描述不能为空", trigger: "blur" }
                ]
            },
            // ==================== 其他信息 ====================
            {
                label: "招聘人数",
                prop: "positionsAvailable",
                search: false,
                width: 80,
                align: "center",
                type: "number",
                span: 8,
                min: 1,
                value: 1,
                rules: [
                    { required: true, message: "招聘人数不能为空", trigger: "blur" },
                    { type: 'number', min: 1, message: '招聘人数不能小于1', trigger: 'blur' }
                ]
            },
            {
                label: "经验要求",
                prop: "experienceRequired",
                search: false,
                span: 8,
                placeholder: "如：1年以上",
                rules: [
                    { max: 50, message: "经验要求不能超过50个字符", trigger: "blur" }
                ]
            },
            {
                label: "联系人",
                prop: "contactPerson",
                search: false,
                span: 8,
                rules: [
                    { required: true, message: "联系人不能为空", trigger: "blur" },
                    { max: 20, message: "联系人不能超过20个字符", trigger: "blur" }
                ]
            },
            {
                label: "联系电话",
                prop: "contactPhone",
                search: false,
                span: 8,
                rules: [
                    { required: true, message: "联系电话不能为空", trigger: "blur" },
                    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ]
            },
            {
                label: "公司名称",
                prop: "companyName",
                search: true,
                searchSpan: 8,
                span: 8,
                rules: [
                    { max: 100, message: "公司名称不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "状态",
                prop: "status",
                search: true,
                searchSpan: 6,
                width: 80,
                align: "center",
                type: "select",
                dicData: statusOptions,
                span: 8,
                slot: true,
                addDisplay: false,
                editDisplay: false,
                value: "published"
            }
        ]
    };
};
