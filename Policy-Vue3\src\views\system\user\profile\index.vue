<template>
   <div class="profile-container">
      <!-- 顶部横幅区域 -->
      <div class="profile-banner">
         <div class="banner-content">
            <div class="avatar-section">
               <userAvatar />
               <div class="user-basic-info">
                  <h2 class="user-name">{{ state.user.userName }}</h2>
                  <p class="user-title">个人中心</p>
               </div>
            </div>
         </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="profile-content">
         <!-- 导航标签 -->
         <div class="profile-nav">
            <el-tabs v-model="selectedTab" class="profile-tabs">
               <el-tab-pane label="个人资料" name="userinfo">
                  <template #label>
                     <span class="tab-label">
                        <svg-icon icon-class="user" />
                        个人资料
                     </span>
                  </template>
               </el-tab-pane>
               <el-tab-pane label="修改密码" name="resetPwd">
                  <template #label>
                     <span class="tab-label">
                        <svg-icon icon-class="lock" />
                        修改密码
                     </span>
                  </template>
               </el-tab-pane>

            </el-tabs>
         </div>

         <!-- 标签页内容 -->
         <div class="tab-content">
            <div v-show="selectedTab === 'userinfo'" class="tab-pane">
               <userInfo :user="state.user" />
            </div>
            <div v-show="selectedTab === 'resetPwd'" class="tab-pane">
               <resetPwd />
            </div>

         </div>

         <!-- 底部联系信息 -->
         <div class="contact-section">
            <h3 class="section-title">联系信息</h3>
            <div class="contact-info-grid">
               <el-card class="contact-card" shadow="hover">
                  <div class="contact-item">
                     <div class="contact-icon">
                        <svg-icon icon-class="phone" />
                     </div>
                     <div class="contact-details">
                        <h4>手机号码</h4>
                        <p>{{ state.user.phonenumber || '未设置' }}</p>
                     </div>
                  </div>
               </el-card>
               <el-card class="contact-card" shadow="hover">
                  <div class="contact-item">
                     <div class="contact-icon">
                        <svg-icon icon-class="email" />
                     </div>
                     <div class="contact-details">
                        <h4>电子邮箱</h4>
                        <p>{{ state.user.email || '未设置' }}</p>
                     </div>
                  </div>
               </el-card>
            </div>
         </div>
      </div>
   </div>
</template>

<script setup name="Profile">
import userAvatar from "./userAvatar"
import userInfo from "./userInfo"
import resetPwd from "./resetPwd"
import { getUserProfile } from "@/api/system/user"

const route = useRoute()
const selectedTab = ref("userinfo")
const state = reactive({
   user: {},
   roleGroup: {},
   postGroup: {}
})

function getUser() {
   getUserProfile().then(response => {
      state.user = response.data
      state.roleGroup = response.roleGroup
      state.postGroup = response.postGroup
   })
}

onMounted(() => {
   const activeTab = route.params && route.params.activeTab
   if (activeTab) {
      selectedTab.value = activeTab
   }
   getUser()
})
</script>

<style scoped>
.profile-container {
   min-height: 100vh;
   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
   padding: 0;
}

.profile-banner {
   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
   color: white;
   padding: 40px 0;
   position: relative;
   overflow: hidden;
}

.profile-banner::before {
   content: '';
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
   opacity: 0.3;
}

.banner-content {
   max-width: 1200px;
   margin: 0 auto;
   padding: 0 20px;
   display: flex;
   justify-content: space-between;
   align-items: center;
   position: relative;
   z-index: 1;
}

.avatar-section {
   display: flex;
   align-items: center;
   gap: 24px;
}

.user-basic-info h2.user-name {
   margin: 0 0 8px 0;
   font-size: 32px;
   font-weight: 600;
   text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-basic-info p.user-title {
   margin: 0 0 16px 0;
   font-size: 16px;
   opacity: 0.9;
   font-weight: 300;
}

.user-badges {
   display: flex;
   gap: 8px;
   flex-wrap: wrap;
}

.user-badges .el-tag {
   background: rgba(255,255,255,0.2);
   border: 1px solid rgba(255,255,255,0.3);
   color: white;
   backdrop-filter: blur(10px);
}

.quick-stats {
   display: flex;
   gap: 40px;
}

.stat-item {
   text-align: center;
}

.stat-value {
   font-size: 20px;
   font-weight: 600;
   margin-bottom: 4px;
   text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-label {
   font-size: 14px;
   opacity: 0.8;
   font-weight: 300;
}

.profile-content {
   background: #f8fafc;
   min-height: calc(100vh - 200px);
   border-radius: 24px 24px 0 0;
   margin-top: -24px;
   position: relative;
   z-index: 2;
}

.profile-nav {
   padding: 32px 32px 0;
   max-width: 1200px;
   margin: 0 auto;
}

.profile-tabs {
   border-bottom: 2px solid #e2e8f0;
}

.profile-tabs :deep(.el-tabs__header) {
   margin: 0;
   border: none;
}

.profile-tabs :deep(.el-tabs__nav-wrap) {
   padding: 0;
}

.profile-tabs :deep(.el-tabs__item) {
   padding: 16px 24px;
   font-size: 16px;
   font-weight: 500;
   color: #64748b;
   border: none;
   margin-right: 8px;
   border-radius: 12px 12px 0 0;
   transition: all 0.3s ease;
}

.profile-tabs :deep(.el-tabs__item:hover) {
   color: #3b82f6;
   background: rgba(59, 130, 246, 0.05);
}

.profile-tabs :deep(.el-tabs__item.is-active) {
   color: #3b82f6;
   background: white;
   box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
}

.profile-tabs :deep(.el-tabs__active-bar) {
   display: none;
}

.tab-label {
   display: flex;
   align-items: center;
   gap: 8px;
}

.tab-content {
   padding: 32px;
   max-width: 1200px;
   margin: 0 auto;
}

.tab-pane {
   background: white;
   border-radius: 16px;
   padding: 32px;
   box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.contact-section {
   padding: 32px;
   max-width: 1200px;
   margin: 0 auto;
}

.section-title {
   font-size: 24px;
   font-weight: 600;
   color: #1e293b;
   margin: 0 0 24px 0;
   text-align: center;
}

.contact-info-grid {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
   gap: 24px;
}

.contact-card {
   border: none;
   border-radius: 16px;
   transition: all 0.3s ease;
}

.contact-card:hover {
   transform: translateY(-4px);
   box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.contact-item {
   display: flex;
   align-items: center;
   gap: 16px;
   padding: 8px;
}

.contact-icon {
   width: 48px;
   height: 48px;
   border-radius: 12px;
   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
   display: flex;
   align-items: center;
   justify-content: center;
   color: white;
   font-size: 20px;
}

.contact-details h4 {
   margin: 0 0 4px 0;
   font-size: 16px;
   font-weight: 600;
   color: #1e293b;
}

.contact-details p {
   margin: 0;
   font-size: 14px;
   color: #64748b;
}

/* 响应式设计 */
@media (max-width: 768px) {
   .banner-content {
      flex-direction: column;
      text-align: center;
      gap: 24px;
   }

   .avatar-section {
      flex-direction: column;
      text-align: center;
   }

   .quick-stats {
      gap: 20px;
   }

   .profile-nav,
   .tab-content {
      padding: 16px;
   }

   .tab-pane {
      padding: 20px;
   }

   .contact-info-grid {
      grid-template-columns: 1fr;
   }
}
</style>
