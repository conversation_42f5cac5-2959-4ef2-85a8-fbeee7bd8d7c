// 用工信息管理表格配置
export function createEmploymentTableOption() {
    return Promise.resolve({
        // 表格列配置
        column: [
            {
                prop: 'employmentId',
                label: '用工ID',
                width: 80,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'title',
                label: '用工标题',
                minWidth: 200,
                align: 'left',
                showOverflowTooltip: true,
                showColumn: true,
                search: true,
                type: 'input',
                placeholder: '请输入用工标题'
            },
            {
                prop: 'employmentType',
                label: '用工类型',
                width: 100,
                align: 'center',
                slot: 'employmentType',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择用工类型',
                dicData: [
                    { label: '日结', value: '日结' },
                    { label: '周结', value: '周结' },
                    { label: '月结', value: '月结' },
                    { label: '计件', value: '计件' }
                ]
            },
            {
                prop: 'workCategory',
                label: '工作类别',
                width: 120,
                align: 'center',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择工作类别',
                dicData: [
                    { label: '服务员', value: '服务员' },
                    { label: '保洁', value: '保洁' },
                    { label: '搬运工', value: '搬运工' },
                    { label: '销售', value: '销售' },
                    { label: '厨师助手', value: '厨师助手' },
                    { label: '快递员', value: '快递员' },
                    { label: '保安', value: '保安' },
                    { label: '临时工', value: '临时工' }
                ]
            },
            {
                prop: 'salaryRange',
                label: '薪资范围',
                width: 150,
                align: 'center',
                slot: 'salaryRange',
                showColumn: true
            },
            {
                prop: 'workLocation',
                label: '工作地点',
                width: 150,
                align: 'center',
                showOverflowTooltip: true,
                showColumn: true
            },
            {
                prop: 'recruitCount',
                label: '招聘人数',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'urgencyLevel',
                label: '紧急程度',
                width: 100,
                align: 'center',
                slot: 'urgencyLevel',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择紧急程度',
                dicData: [
                    { label: '紧急', value: 'urgent' },
                    { label: '高', value: 'high' },
                    { label: '普通', value: 'normal' },
                    { label: '低', value: 'low' }
                ]
            },
            {
                prop: 'status',
                label: '状态',
                width: 100,
                align: 'center',
                slot: 'status',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择状态',
                dicData: [
                    { label: '草稿', value: 'draft' },
                    { label: '已发布', value: 'published' },
                    { label: '已暂停', value: 'paused' },
                    { label: '已关闭', value: 'closed' },
                    { label: '已完成', value: 'completed' }
                ]
            },
            {
                prop: 'createTime',
                label: '创建时间',
                width: 180,
                align: 'center',
                showColumn: true,
                formatter: (row) => {
                    return row.createTime ? row.createTime.substring(0, 16) : ''
                }
            }
        ]
    })
}
