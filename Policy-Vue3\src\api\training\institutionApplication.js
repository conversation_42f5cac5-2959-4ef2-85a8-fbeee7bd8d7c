import request from '@/utils/request'

// 查询培训机构申请列表
export function listTrainingInstitutionApplication(query) {
  return request({
    url: '/training/institution/application/list',
    method: 'get',
    params: query
  })
}

// 查询培训机构申请详细
export function getTrainingInstitutionApplication(applicationId) {
  return request({
    url: '/training/institution/application/' + applicationId,
    method: 'get'
  })
}

// 新增培训机构申请
export function addTrainingInstitutionApplication(data) {
  return request({
    url: '/training/institution/application',
    method: 'post',
    data: data
  })
}

// 修改培训机构申请
export function updateTrainingInstitutionApplication(data) {
  return request({
    url: '/training/institution/application',
    method: 'put',
    data: data
  })
}

// 删除培训机构申请
export function delTrainingInstitutionApplication(applicationId) {
  return request({
    url: '/training/institution/application/' + applicationId,
    method: 'delete'
  })
}

// 审核培训机构申请
export function reviewTrainingInstitutionApplication(applicationId, status, reviewComment) {
  return request({
    url: '/training/institution/application/review/' + applicationId,
    method: 'put',
    params: {
      status: status,
      reviewComment: reviewComment
    }
  })
}

// 批量审核培训机构申请
export function batchReviewTrainingInstitutionApplication(applicationIds, status, reviewComment) {
  return request({
    url: '/training/institution/application/batch-review',
    method: 'put',
    params: {
      applicationIds: applicationIds,
      status: status,
      reviewComment: reviewComment
    }
  })
}

// 取消培训机构申请
export function cancelTrainingInstitutionApplication(applicationId) {
  return request({
    url: '/training/institution/application/cancel/' + applicationId,
    method: 'put'
  })
}

// 获取某个培训订单的机构申请列表
export function getApplicationsByOrderId(orderId) {
  return request({
    url: '/training/institution/application/order/' + orderId,
    method: 'get'
  })
}

// 统计某个培训订单的机构申请数量
export function countApplicationsByOrderId(orderId) {
  return request({
    url: '/training/institution/application/count/' + orderId,
    method: 'get'
  })
}

// 统计某个培训订单的已通过机构申请数量
export function countApprovedApplicationsByOrderId(orderId) {
  return request({
    url: '/training/institution/application/approved-count/' + orderId,
    method: 'get'
  })
}

// 根据申请状态统计数量
export function countByStatus(status) {
  return request({
    url: '/training/institution/application/count-by-status/' + status,
    method: 'get'
  })
}

// 获取待审核的申请列表
export function getPendingApplications() {
  return request({
    url: '/training/institution/application/pending',
    method: 'get'
  })
}

// ========== 公开API接口（无需登录） ==========

// 提交培训机构申请（公开接口）
export function submitTrainingInstitutionApplication(data) {
  return request({
    url: '/public/training/institution/application/submit',
    method: 'post',
    data: data
  })
}

// 更新培训机构申请（公开接口）
export function updateTrainingInstitutionApplicationPublic(data) {
  return request({
    url: '/public/training/institution/application/update',
    method: 'put',
    data: data
  })
}

// 检查机构申请状态（公开接口）
export function checkInstitutionApplicationStatus(orderId, institutionName, contactPhone, userId) {
  return request({
    url: '/public/training/institution/application/check-status',
    method: 'get',
    params: {
      orderId: orderId,
      institutionName: institutionName,
      contactPhone: contactPhone,
      userId: userId
    }
  })
}

// 获取当前登录用户的机构申请状态
export function getMyInstitutionApplicationStatus(orderId) {
  return request({
    url: '/public/training/institution/application/my-status/' + orderId,
    method: 'get'
  })
}

// 获取当前登录用户的所有机构申请记录
export function getMyInstitutionApplications() {
  return request({
    url: '/public/training/institution/application/my-applications',
    method: 'get'
  })
}

// 取消我的机构申请
export function cancelMyInstitutionApplication(applicationId) {
  return request({
    url: '/public/training/institution/application/cancel/' + applicationId,
    method: 'put'
  })
}

// 获取培训订单的机构申请统计信息（公开接口）
export function getInstitutionApplicationStatistics(orderId) {
  return request({
    url: '/public/training/institution/application/statistics/' + orderId,
    method: 'get'
  })
}

// 获取某个培训订单的已通过机构申请列表（公开接口）
export function getApprovedInstitutionApplications(orderId) {
  return request({
    url: '/public/training/institution/application/approved/' + orderId,
    method: 'get'
  })
}
