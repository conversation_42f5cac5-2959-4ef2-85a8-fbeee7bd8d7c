/**
 * 培训机构申请表格配置
 */
export function createInstitutionApplicationTableOption(proxy) {
  return {
    // 表格列配置
    column: [
      {
        label: '申请ID',
        prop: 'applicationId',
        width: 80,
        hide: true,
        search: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: true
      },
      {
        label: '培训订单',
        prop: 'orderId',
        width: 100,
        hide: false,
        search: true,
        searchType: 'select',
        searchOptions: [], // 可以通过API获取培训订单列表
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'select',
        dicUrl: '/training/order/list',
        dicMethod: 'get',
        props: {
          label: 'orderTitle',
          value: 'orderId'
        }
      },
      {
        label: '订单标题',
        prop: 'orderTitle',
        width: 200,
        hide: false,
        search: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: true
      },
      {
        label: '机构名称',
        prop: 'institutionName',
        width: 200,
        hide: false,
        search: true,
        searchType: 'input',
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'input',
        maxlength: 200
      },
      {
        label: '机构代码',
        prop: 'institutionCode',
        width: 150,
        hide: false,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'input',
        maxlength: 50
      },
      {
        label: '法定代表人',
        prop: 'legalPerson',
        width: 120,
        hide: false,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'input',
        maxlength: 50
      },
      {
        label: '联系人',
        prop: 'contactPerson',
        width: 100,
        hide: false,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'input',
        maxlength: 50
      },
      {
        label: '联系电话',
        prop: 'contactPhone',
        width: 120,
        hide: false,
        search: true,
        searchType: 'input',
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'input',
        maxlength: 20
      },
      {
        label: '联系邮箱',
        prop: 'contactEmail',
        width: 150,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'input',
        maxlength: 100
      },
      {
        label: '机构地址',
        prop: 'institutionAddress',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'textarea',
        maxlength: 500
      },
      {
        label: '机构类型',
        prop: 'institutionType',
        width: 100,
        hide: false,
        search: true,
        searchType: 'select',
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'select',
        dicData: [
          { label: '企业', value: '企业' },
          { label: '事业单位', value: '事业单位' },
          { label: '社会组织', value: '社会组织' },
          { label: '其他', value: '其他' }
        ]
      },
      {
        label: '成立时间',
        prop: 'establishedDate',
        width: 120,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      },
      {
        label: '注册资本',
        prop: 'registeredCapital',
        width: 100,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'number',
        precision: 2,
        suffix: '万元'
      },
      {
        label: '经营范围',
        prop: 'businessScope',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'textarea',
        rows: 3
      },
      {
        label: '培训经验',
        prop: 'trainingExperience',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'textarea',
        rows: 4
      },
      {
        label: '培训能力',
        prop: 'trainingCapacity',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'textarea',
        rows: 4
      },
      {
        label: '培训计划',
        prop: 'trainingPlan',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'textarea',
        rows: 4
      },
      {
        label: '师资信息',
        prop: 'teacherInfo',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        required: true,
        type: 'textarea',
        rows: 4
      },
      {
        label: '设施设备',
        prop: 'facilityInfo',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'textarea',
        rows: 3
      },
      {
        label: '申请状态',
        prop: 'applicationStatus',
        width: 100,
        hide: false,
        search: true,
        searchType: 'select',
        addDisplay: false,
        editDisplay: false,
        viewDisplay: true,
        type: 'select',
        dicData: [
          { label: '待审核', value: '0' },
          { label: '已通过', value: '1' },
          { label: '已拒绝', value: '2' },
          { label: '已取消', value: '3' }
        ]
      },
      {
        label: '申请时间',
        prop: 'applicationTime',
        width: 150,
        hide: false,
        search: true,
        searchType: 'daterange',
        addDisplay: false,
        editDisplay: false,
        viewDisplay: true,
        type: 'datetime',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      {
        label: '审核时间',
        prop: 'reviewTime',
        width: 150,
        hide: false,
        search: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: true,
        type: 'datetime',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      {
        label: '审核人',
        prop: 'reviewer',
        width: 100,
        hide: false,
        search: true,
        searchType: 'input',
        addDisplay: false,
        editDisplay: false,
        viewDisplay: true,
        type: 'input'
      },
      {
        label: '审核意见',
        prop: 'reviewComment',
        width: 200,
        hide: true,
        search: false,
        addDisplay: false,
        editDisplay: false,
        viewDisplay: true,
        type: 'textarea',
        rows: 3
      },
      {
        label: '申请备注',
        prop: 'applicationNote',
        width: 200,
        hide: true,
        search: false,
        addDisplay: true,
        editDisplay: true,
        viewDisplay: true,
        type: 'textarea',
        rows: 3
      }
    ],
    // 表单配置
    formOption: {
      gutter: 20,
      labelWidth: 120,
      span: 12,
      menuBtn: true,
      submitBtn: true,
      emptyBtn: true,
      menuPosition: 'center'
    },
    // 其他配置
    searchMenuSpan: 6,
    border: true,
    index: true,
    viewBtn: true,
    editBtn: true,
    delBtn: true,
    addBtn: true,
    selection: true,
    dialogClickModal: false,
    dialogEscape: false,
    tip: false
  }
}
