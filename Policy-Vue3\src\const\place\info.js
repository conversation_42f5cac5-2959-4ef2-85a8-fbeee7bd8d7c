// 场地信息表格配置
export function createPlaceInfoTableOption(proxy) {
  return {
    // 表格列配置
    columns: [
      {
        prop: 'placeId',
        label: '场地ID',
        width: 80,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: false
      },
      {
        prop: 'placeName',
        label: '场地名称',
        minWidth: 200,
        align: 'left',
        showOverflowTooltip: true,
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入场地名称'
      },
      {
        prop: 'placeCode',
        label: '场地编码',
        width: 120,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        formType: 'input',
        placeholder: '请输入场地编码'
      },
      {
        prop: 'placeType',
        label: '场地类型',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        required: true,
        formType: 'select',
        placeholder: '请选择场地类型',
        slotName: 'placeType',
        options: [
          { label: '创业园区', value: '创业园区' },
          { label: '孵化器', value: '孵化器' },
          { label: '众创空间', value: '众创空间' },
          { label: '产业园', value: '产业园' }
        ]
      },
      {
        prop: 'placeLevel',
        label: '场地等级',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        formType: 'select',
        placeholder: '请选择场地等级',
        slotName: 'placeLevel',
        options: [
          { label: '国家级', value: '国家级' },
          { label: '省级', value: '省级' },
          { label: '市级', value: '市级' },
          { label: '区级', value: '区级' }
        ]
      },
      {
        prop: 'address',
        label: '详细地址',
        minWidth: 200,
        align: 'left',
        showOverflowTooltip: true,
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入详细地址'
      },
      {
        prop: 'regionName',
        label: '区域',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: false
      },
      {
        prop: 'regionCode',
        label: '区域代码',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: true,
        showInForm: true,
        formType: 'select',
        placeholder: '请选择区域',
        options: [
          { label: '市南区', value: '370202' },
          { label: '市北区', value: '370203' },
          { label: '崂山区', value: '370212' }
        ]
      },
      {
        prop: 'placeArea',
        label: '场地面积',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入场地面积（平方米）'
      },
      {
        prop: 'usableArea',
        label: '可使用面积',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入可使用面积（平方米）'
      },
      {
        prop: 'contactPerson',
        label: '联系人',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入联系人'
      },
      {
        prop: 'contactPhone',
        label: '联系电话',
        width: 120,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入联系电话'
      },
      {
        prop: 'contactEmail',
        label: '联系邮箱',
        width: 150,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'input',
        placeholder: '请输入联系邮箱'
      },
      {
        prop: 'companyCount',
        label: '入驻企业',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入入驻企业数'
      },
      {
        prop: 'availablePositions',
        label: '可用工位',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入可提供工位数'
      },
      {
        prop: 'occupiedPositions',
        label: '已占工位',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入已占用工位数'
      },
      {
        prop: 'positionUsage',
        label: '工位使用率',
        width: 120,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: false,
        slotName: 'positionUsage'
      },
      {
        prop: 'rentRange',
        label: '租金范围',
        width: 150,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: false,
        slotName: 'rentRange'
      },
      {
        prop: 'rentPriceMin',
        label: '最低租金',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '元/月/平方米'
      },
      {
        prop: 'rentPriceMax',
        label: '最高租金',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '元/月/平方米'
      },
      {
        prop: 'operationMode',
        label: '运营模式',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'select',
        placeholder: '请选择运营模式',
        options: [
          { label: '自营', value: '自营' },
          { label: '委托运营', value: '委托运营' },
          { label: '合作运营', value: '合作运营' }
        ]
      },
      {
        prop: 'industryDirection',
        label: '行业方向',
        width: 150,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'input',
        placeholder: '多个用逗号分隔'
      },
      {
        prop: 'description',
        label: '场地描述',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'textarea',
        placeholder: '请输入场地详细描述'
      },
      {
        prop: 'status',
        label: '状态',
        width: 80,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        formType: 'select',
        placeholder: '请选择状态',
        slotName: 'status',
        options: [
          { label: '正常', value: '0' },
          { label: '停用', value: '1' }
        ]
      },
      {
        prop: 'isFeatured',
        label: '是否推荐',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'radio',
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ]
      },
      {
        prop: 'isOpenSettle',
        label: '开放入驻',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'radio',
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ]
      },
      {
        prop: 'viewCount',
        label: '浏览次数',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: false
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: 160,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: false,
        formType: 'daterange',
        placeholder: '请选择创建时间'
      }
    ],
    
    // 表单配置
    formOptions: {
      labelWidth: '120px',
      size: 'default',
      rules: {
        placeName: [
          { required: true, message: '场地名称不能为空', trigger: 'blur' }
        ],
        placeType: [
          { required: true, message: '场地类型不能为空', trigger: 'change' }
        ],
        address: [
          { required: true, message: '详细地址不能为空', trigger: 'blur' }
        ],
        contactPerson: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' }
        ]
      }
    }
  }
}
