<template>
  <div class="place-info-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="placeInfoList" :loading="tableLoading" :showIndex="true"
      :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作" operationWidth="280"
      :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['place:info:add']">新 增</el-button>
        <el-button type="warning" plain class="custom-btn" @click="handleExport" v-hasPermi="['place:info:export']">导 出</el-button>
      </template>

      <!-- 场地类型列插槽 -->
      <template #placeType="{ row }">
        <el-tag :type="getPlaceTypeTagType(row.placeType)">
          {{ row.placeType }}
        </el-tag>
      </template>

      <!-- 场地等级列插槽 -->
      <template #placeLevel="{ row }">
        <el-tag :type="getPlaceLevelTagType(row.placeLevel)">
          {{ row.placeLevel }}
        </el-tag>
      </template>

      <!-- 租金范围列插槽 -->
      <template #rentRange="{ row }">
        <span v-if="row.rentPriceMin && row.rentPriceMax" class="rent-range">
          ¥{{ row.rentPriceMin }}-{{ row.rentPriceMax }}/月/㎡
        </span>
        <span v-else-if="row.rentPriceMin" class="rent-range">
          ¥{{ row.rentPriceMin }}/月/㎡起
        </span>
        <span v-else class="rent-range">面议</span>
      </template>

      <!-- 工位使用率列插槽 -->
      <template #positionUsage="{ row }">
        <div class="position-usage">
          <span>{{ row.occupiedPositions || 0 }}/{{ row.availablePositions || 0 }}</span>
          <el-progress
            v-if="row.availablePositions > 0"
            :percentage="Math.round((row.occupiedPositions || 0) / row.availablePositions * 100)"
            :stroke-width="6"
            :show-text="false"
            style="margin-top: 4px;"
          />
        </div>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="primary" link @click="handleUpdate(row)" v-hasPermi="['place:info:edit']">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['place:info:remove']">删除</el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="Loading table configuration..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <PlaceInfoFormDialog ref="placeInfoFormDialogRef" :formFields="formFields" :formOption="formOption"
      @submit="handleFormSubmit" @cancel="handleFormCancel" />


  </div>
</template>

<script setup name="PlaceInfo">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue'
import { listPlaceInfo, getPlaceInfo, delPlaceInfo, addPlaceInfo, updatePlaceInfo } from "@/api/place/info"
import { createPlaceInfoTableOption } from "@/const/place/info"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import TableList from '@/components/TableList/index.vue'
import PlaceInfoFormDialog from './PlaceInfoFormDialog.vue'

const { proxy } = getCurrentInstance()

const placeInfoList = ref([])
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
  dialogWidth: '1200px',
  dialogHeight: '80vh'
})
const tableListRef = ref(null)
const placeInfoFormDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    placeName: undefined,
    placeType: undefined,
    placeLevel: undefined,
    regionCode: undefined,
    status: undefined
  }
})

const { queryParams } = toRefs(data)

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  getList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createPlaceInfoTableOption(proxy);

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置
    formOption.value = {
      ...formOption.value,
      ...formOptions
    };

    isTableReady.value = true;
  } catch (error) {
    isTableReady.value = false;
    console.error('初始化配置失败:', error);
  }
};

/** 查询场地信息列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listPlaceInfo(params).then(res => {
    tableLoading.value = false
    loading.value = false
    if (res.code === 200) {
      placeInfoList.value = res.rows || []
      total.value = res.total || 0
    } else {
      placeInfoList.value = []
      total.value = 0
      proxy.$modal.msgError(res.msg || '获取场地信息列表失败')
    }
  }).catch(error => {
    tableLoading.value = false
    loading.value = false
    placeInfoList.value = []
    total.value = 0
    console.error('获取场地信息列表失败:', error)
    proxy.$modal.msgError('获取场地信息列表失败')
  })
}
// TableList 组件事件处理
const handleSearch = (searchData) => {
  searchParams.value = searchData
  queryParams.value.pageNum = 1
  getList()
}

const resetSearch = () => {
  searchParams.value = {}
  queryParams.value.pageNum = 1
  getList()
}

const handleCurrentChange = (page) => {
  queryParams.value.pageNum = page
  getList()
}

const handleSizeChange = (size) => {
  queryParams.value.pageSize = size
  queryParams.value.pageNum = 1
  getList()
}

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.placeId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 操作方法
const handleAdd = () => {
  placeInfoFormDialogRef.value.openDialog('add', {})
}

const handleView = (row) => {
  placeInfoFormDialogRef.value.openDialog('view', row)
}

const handleUpdate = (row) => {
  placeInfoFormDialogRef.value.openDialog('edit', row)
}

const handleDelete = async (row) => {
  try {
    await proxy.$modal.confirm('确认要删除该场地信息吗？')
    await delPlaceInfo(row.placeId)
    proxy.$modal.msgSuccess('删除成功')
    getList()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

const handleExport = () => {
  proxy.download('place/info/export', {
    ...queryParams.value
  }, `place_info_${new Date().getTime()}.xlsx`)
}

// 表单处理方法
const handleFormSubmit = async (formData, mode) => {
  try {
    if (mode === 'add') {
      await addPlaceInfo(formData)
      proxy.$modal.msgSuccess('新增成功')
    } else if (mode === 'edit') {
      await updatePlaceInfo(formData)
      proxy.$modal.msgSuccess('修改成功')
    }
    getList()
  } catch (error) {
    console.error('操作失败:', error)
    proxy.$modal.msgError('操作失败')
  }
}

const handleFormCancel = () => {
  // 表单取消处理
}

// 辅助方法
const getPlaceTypeTagType = (type) => {
  const typeMap = {
    '创业园区': 'primary',
    '孵化器': 'success',
    '众创空间': 'warning',
    '产业园': 'info'
  }
  return typeMap[type] || 'default'
}

const getPlaceLevelTagType = (level) => {
  const levelMap = {
    '国家级': 'danger',
    '省级': 'warning',
    '市级': 'primary',
    '区级': 'info'
  }
  return levelMap[level] || 'default'
}

const getStatusTagType = (status) => {
  return status === '0' ? 'success' : 'danger'
}

const getStatusText = (status) => {
  return status === '0' ? '正常' : '停用'
}
</script>

<style scoped>
.place-info-container {
  padding: 20px;
}

.operation-btns {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.rent-range {
  font-weight: 500;
  color: #e6a23c;
}

.position-usage {
  min-width: 80px;
}

.custom-btn {
  margin-right: 10px;
}

.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
</style>

