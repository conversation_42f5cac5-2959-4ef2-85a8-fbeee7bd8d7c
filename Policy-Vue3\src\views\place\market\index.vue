<template>
    <div class="market-container app-container">
        <!-- 使用 TableList 组件 -->
        <TableList v-if="isTableReady" :columns="tableColumns" :data="marketInfoList" :loading="tableLoading"
            :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
            operationWidth="280" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
            :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
            @current-change="handleCurrentChange" @size-change="handleSizeChange"
            @selection-change="handleSelectionChange">

            <!-- 左侧按钮插槽 -->
            <template #menu-left>
                <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['place:market:add']">新 增</el-button>
                <el-button type="warning" plain class="custom-btn" @click="handleExport" v-hasPermi="['place:market:export']">导 出</el-button>
            </template>

            <!-- 市场类型列插槽 -->
            <template #marketType="{ row }">
                <el-tag :type="getMarketTypeTagType(row.marketType)">
                    {{ row.marketType }}
                </el-tag>
            </template>

            <!-- 是否推荐列插槽 -->
            <template #isFeatured="{ row }">
                <el-tag :type="row.isFeatured ? 'success' : 'info'">
                    {{ row.isFeatured ? '是' : '否' }}
                </el-tag>
            </template>

            <!-- 状态列插槽 -->
            <template #status="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                    {{ getStatusText(row.status) }}
                </el-tag>
            </template>

            <!-- 操作列插槽 -->
            <template #menu="{ row }">
                <div class="operation-btns">
                    <el-button type="primary" link @click="handleDetail(row)" v-hasPermi="['place:market:query']">详情</el-button>
                    <el-button type="success" link @click="handleUpdate(row)" v-hasPermi="['place:market:edit']">修改</el-button>
                    <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['place:market:remove']">删除</el-button>
                </div>
            </template>
        </TableList>

        <div v-else class="loading-placeholder">
            <el-empty description="Loading table configuration..."></el-empty>
        </div>

        <!-- 市场信息表单弹窗组件 -->
        <MarketFormDialog ref="marketFormDialogRef" :formFields="formFields" :formOption="formOption"
            @submit="handleFormSubmit" @cancel="handleFormCancel" />

        <!-- 市场详情弹窗组件 -->
        <MarketDetailDialog ref="marketDetailDialogRef" />
    </div>


</template>

<script setup name="LaborMarketInfo">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue'
import {
    listLaborMarketInfo,
    getLaborMarketInfo,
    delLaborMarketInfo,
    addLaborMarketInfo,
    updateLaborMarketInfo
} from "@/api/place/market"
import { createMarketTableOption } from "@/const/place/market"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import TableList from '@/components/TableList/index.vue'
import MarketFormDialog from './MarketFormDialog.vue'
import MarketDetailDialog from './MarketDetailDialog.vue'

const { proxy } = getCurrentInstance()

const marketInfoList = ref([])
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([])
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
    dialogWidth: '1000px',
    dialogHeight: '70vh'
})
const tableListRef = ref(null)
const marketFormDialogRef = ref(null)
const marketDetailDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        marketName: null,
        marketCode: null,
        marketType: null,
        regionCode: null,
        status: null,
    }
})

const { queryParams } = toRefs(data)

// 辅助函数
function getMarketTypeTagType(marketType) {
    const typeMap = {
        '综合市场': 'primary',
        '专业市场': 'success',
        '临时市场': 'warning'
    }
    return typeMap[marketType] || 'info'
}

function getStatusTagType(status) {
    return status === '0' ? 'success' : 'danger'
}

function getStatusText(status) {
    return status === '0' ? '正常' : '停用'
}

// 初始化表格配置
async function initTableConfig() {
    try {
        const tableOption = await createMarketTableOption()
        const { tableColumns: cols, searchColumns, formFields: fields } = extractTableColumns(tableOption)

        tableColumns.value = cols
        searchableColumns.value = searchColumns
        formFields.value = fields

        isTableReady.value = true
    } catch (error) {
        console.error('初始化表格配置失败:', error)
        proxy.$modal.msgError('表格配置加载失败')
    }
}

// 查询数据列表
function getList() {
    tableLoading.value = true
    listLaborMarketInfo(queryParams.value).then(response => {
        marketInfoList.value = response.rows || []
        total.value = response.total || 0
        tableLoading.value = false
    }).catch(() => {
        tableLoading.value = false
    })
}

// 搜索处理
function handleSearch(searchData) {
    searchParams.value = searchData
    Object.assign(queryParams.value, searchData)
    queryParams.value.pageNum = 1
    getList()
}

// 重置搜索
function resetSearch() {
    searchParams.value = {}
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        marketName: null,
        marketCode: null,
        marketType: null,
        regionCode: null,
        status: null,
    }
    getList()
}

// 分页处理
function handleCurrentChange(page) {
    queryParams.value.pageNum = page
    getList()
}

function handleSizeChange(size) {
    queryParams.value.pageSize = size
    queryParams.value.pageNum = 1
    getList()
}

// 选择处理
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.marketId)
    single.value = selection.length !== 1
    multiple.value = !selection.length
}

// 新增
function handleAdd() {
    marketFormDialogRef.value?.openDialog('add')
}

// 修改
function handleUpdate(row) {
    const marketId = row?.marketId || ids.value[0]
    if (marketId) {
        marketFormDialogRef.value?.openDialog('edit', marketId)
    }
}

// 详情
function handleDetail(row) {
    marketDetailDialogRef.value?.openDialog(row.marketId)
}

// 删除
function handleDelete(row) {
    const marketIds = row?.marketId ? [row.marketId] : ids.value
    if (marketIds.length === 0) {
        proxy.$modal.msgWarning('请选择要删除的数据')
        return
    }

    proxy.$modal.confirm(`是否确认删除选中的${marketIds.length}条记录？`).then(() => {
        return delLaborMarketInfo(marketIds.join(','))
    }).then(() => {
        getList()
        proxy.$modal.msgSuccess('删除成功')
    }).catch(() => {})
}

// 导出
function handleExport() {
    proxy.download('place/market/export', {
        ...queryParams.value
    }, `labor_market_info_${new Date().getTime()}.xlsx`)
}

// 表单提交处理
function handleFormSubmit() {
    getList()
}

// 表单取消处理
function handleFormCancel() {
    // 可以在这里处理取消逻辑
}

onMounted(async () => {
    await initTableConfig()
    getList()
})
</script>

<style scoped>
.market-container {
    padding: 20px;
}

.operation-btns {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.custom-btn {
    margin-right: 10px;
}

.loading-placeholder {
    text-align: center;
    padding: 50px 0;
}
</style>
