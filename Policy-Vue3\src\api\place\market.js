import request from '@/utils/request'

// 查询零工市场基础信息列表
export function listLaborMarketInfo(query) {
  return request({
    url: '/place/market/list',
    method: 'get',
    params: query
  })
}

// 查询零工市场基础信息详细
export function getLaborMarketInfo(marketId) {
  return request({
    url: '/place/market/' + marketId,
    method: 'get'
  })
}

// 查询零工市场详细信息（包含关联信息）
export function getLaborMarketInfoDetail(marketId) {
  return request({
    url: '/place/market/detail/' + marketId,
    method: 'get'
  })
}

// 新增零工市场基础信息
export function addLaborMarketInfo(data) {
  return request({
    url: '/place/market',
    method: 'post',
    data: data
  })
}

// 修改零工市场基础信息
export function updateLaborMarketInfo(data) {
  return request({
    url: '/place/market',
    method: 'put',
    data: data
  })
}

// 删除零工市场基础信息
export function delLaborMarketInfo(marketId) {
  return request({
    url: '/place/market/' + marketId,
    method: 'delete'
  })
}

// 查询推荐零工市场信息列表
export function listFeaturedLaborMarketInfo(query) {
  return request({
    url: '/place/market/featured',
    method: 'get',
    params: query
  })
}

// 查询活跃零工市场信息列表
export function listActiveLaborMarketInfo(query) {
  return request({
    url: '/place/market/active',
    method: 'get',
    params: query
  })
}

// 根据市场类型查询零工市场信息列表
export function getLaborMarketInfoByType(marketType) {
  return request({
    url: '/place/market/type/' + marketType,
    method: 'get'
  })
}

// 根据区域代码查询零工市场信息列表
export function getLaborMarketInfoByRegion(regionCode) {
  return request({
    url: '/place/market/region/' + regionCode,
    method: 'get'
  })
}

// 根据服务类别查询零工市场信息列表
export function getLaborMarketInfoByServiceCategory(serviceCategory) {
  return request({
    url: '/place/market/service/' + serviceCategory,
    method: 'get'
  })
}

// 查询零工市场统计信息
export function getLaborMarketInfoStatistics() {
  return request({
    url: '/place/market/statistics',
    method: 'get'
  })
}

// 根据关键词搜索零工市场信息
export function searchLaborMarketInfo(keyword) {
  return request({
    url: '/place/market/search',
    method: 'get',
    params: { keyword }
  })
}

// 获取所有市场类型列表
export function getAllMarketTypes() {
  return request({
    url: '/place/market/types',
    method: 'get'
  })
}

// 获取所有区域列表
export function getAllMarketRegions() {
  return request({
    url: '/place/market/regions',
    method: 'get'
  })
}

// 获取所有服务类别列表
export function getAllServiceCategories() {
  return request({
    url: '/place/market/services',
    method: 'get'
  })
}

// 根据容量范围查询零工市场信息
export function getLaborMarketInfoByCapacityRange(minCapacity, maxCapacity) {
  return request({
    url: '/place/market/capacity',
    method: 'get',
    params: { minCapacity, maxCapacity }
  })
}

// 根据费用范围查询零工市场信息
export function getLaborMarketInfoByFeeRange(minFee, maxFee) {
  return request({
    url: '/place/market/fee',
    method: 'get',
    params: { minFee, maxFee }
  })
}

// 查询高需求零工市场（根据日均用工需求排序）
export function getHighDemandLaborMarketInfo(limit) {
  return request({
    url: '/place/market/high-demand',
    method: 'get',
    params: { limit }
  })
}
