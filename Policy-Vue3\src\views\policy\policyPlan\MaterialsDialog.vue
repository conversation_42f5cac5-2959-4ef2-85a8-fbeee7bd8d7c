<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" :close-on-click-modal="false"
    :close-on-press-escape="false" append-to-body>
    <div class="materials-container">
      <!-- 申请基本信息 -->
      <div class="application-info">
        <h4>申请人信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请ID">{{ applicationData.applicationId }}</el-descriptions-item>
          <el-descriptions-item label="政策名称">{{ applicationData.policyName }}</el-descriptions-item>
          <el-descriptions-item label="申请人姓名">{{ applicationData.applicantName || applicationData.applicantUserName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ applicationData.applicantPhone || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="申请人账号">{{ applicationData.applicantUserName }}</el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ parseTime(applicationData.submitTime) }}</el-descriptions-item>
          <el-descriptions-item label="当前状态" :span="2">
            <el-tag :type="getStatusTagType(applicationData.applicationStatus)" size="small">
              {{ getStatusText(applicationData.applicationStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="applicationData.remark" label="备注信息" :span="2">
            {{ applicationData.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 企业基本信息 -->
      <div class="company-info" v-if="hasCompanyInfo">
        <h4>企业基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="企业名称">{{ applicationData.companyName || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="法定代表人">{{ applicationData.companyLegalPerson || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码" :span="2">{{ applicationData.companyCode || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="企业注册地址" :span="2">{{ applicationData.companyAddress || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="企业联系人">{{ applicationData.companyContactPerson || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="企业联系电话">{{ applicationData.companyContactPhone || '未填写' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 银行开户信息 -->
      <div class="bank-info" v-if="hasBankInfo">
        <h4>银行开户信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="开户银行">{{ applicationData.bankName || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="账户名称">{{ applicationData.bankAccountName || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="银行账号" :span="2">{{ applicationData.bankAccountNumber || '未填写' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 材料文件列表 -->
      <div class="materials-list">
        <h4>申请材料文件</h4>
        <div v-if="materialsList.length > 0" class="materials-grid">
          <div v-for="(material, index) in materialsList" :key="index" class="material-card"
            :class="{ 'required': material.required, 'uploaded': material.uploaded }">
            <div class="material-header">
              <div class="material-icon">
                <el-icon v-if="material.uploaded" class="success-icon">
                  <CircleCheck />
                </el-icon>
                <el-icon v-else class="pending-icon">
                  <Clock />
                </el-icon>
              </div>
              <div class="material-status">
                <el-tag v-if="material.required" type="danger" size="small">必需</el-tag>
                <el-tag v-else type="info" size="small">可选</el-tag>
                <el-tag v-if="material.uploaded" type="success" size="small">已上传</el-tag>
                <el-tag v-else type="warning" size="small">未上传</el-tag>
              </div>
            </div>

            <div class="material-content">
              <div class="material-name">{{ material.name }}</div>

              <div v-if="material.files && material.files.length > 0" class="files-list">
                <div v-for="(file, fileIndex) in material.files" :key="fileIndex" class="file-item">
                  <FileView :file="file" />
                </div>
              </div>

              <div v-else class="no-file">
                <el-icon class="no-file-icon">
                  <Document />
                </el-icon>
                <span>未上传文件</span>
              </div>
            </div>
          </div>
        </div>

        <el-empty v-else description="暂无材料信息" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup>
import { ref, computed } from 'vue'
import { parseTime } from "@/utils/ruoyi"
import { CircleCheck, Clock, Document } from '@element-plus/icons-vue'
import FileView from '@/components/FileView/index.vue'

const dialogVisible = ref(false)
const dialogTitle = ref('申请材料')
const applicationData = ref({})
const materialsList = ref([])

// 计算属性：是否有企业信息
const hasCompanyInfo = computed(() => {
  const data = applicationData.value
  return data.companyName || data.companyCode || data.companyLegalPerson ||
         data.companyAddress || data.companyContactPerson || data.companyContactPhone
})

// 计算属性：是否有银行信息
const hasBankInfo = computed(() => {
  const data = applicationData.value
  return data.bankName || data.bankAccountName || data.bankAccountNumber
})

// 打开弹窗
const openDialog = (data) => {
  dialogVisible.value = true
  dialogTitle.value = `申请材料 - ${data.policyName}`
  applicationData.value = { ...data }

  // 解析材料数据
  try {
    const materials = typeof data.requiredMaterials === 'string'
      ? JSON.parse(data.requiredMaterials)
      : data.requiredMaterials || []

    // 确保每个材料都有files数组
    materialsList.value = materials.map(material => ({
      ...material,
      files: material.files || []
    }))
  } catch (error) {
    console.error('解析材料数据失败:', error)
    materialsList.value = []
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  applicationData.value = {}
  materialsList.value = []
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',  // 待初审
    '1': 'success',  // 初审通过
    '2': 'danger',   // 初审拒绝
    '3': 'warning',  // 待终审
    '4': 'success',  // 终审通过
    '5': 'danger',   // 终审拒绝
    '6': 'info'      // 已完成
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '待初审',
    '1': '初审通过',
    '2': '初审拒绝',
    '3': '待终审',
    '4': '终审通过',
    '5': '终审拒绝',
    '6': '已完成'
  }
  return statusMap[status] || '未知状态'
}





// 暴露方法给父组件
defineExpose({
  openDialog
})
</script>

<style scoped>
.materials-container {
  max-height: 70vh;
  overflow-y: auto;
}

.application-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.application-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.company-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.company-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.bank-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f0f9f0;
  border-radius: 8px;
  border-left: 4px solid #67c23a;
}

.bank-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.materials-list h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.material-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.material-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.material-card.required {
  border-left: 4px solid #f56c6c;
}

.material-card.uploaded {
  border-left: 4px solid #67c23a;
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.material-icon {
  font-size: 20px;
}

.success-icon {
  color: #67c23a;
}

.pending-icon {
  color: #e6a23c;
}

.material-status {
  display: flex;
  gap: 8px;
}

.material-content {
  min-height: 80px;
}

.material-name {
  font-weight: 500;
  color: #303133;
  line-height: 1.5;
  margin-bottom: 12px;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.no-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  background-color: #fafafa;
  border-radius: 4px;
}

.no-file-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.file-preview {
  text-align: center;
}

.unsupported-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
}

.large-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}

/* 滚动条样式 */
.materials-container::-webkit-scrollbar {
  width: 6px;
}

.materials-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.materials-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.materials-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .materials-grid {
    grid-template-columns: 1fr;
  }

  .file-actions {
    flex-direction: column;
  }
}
</style>
