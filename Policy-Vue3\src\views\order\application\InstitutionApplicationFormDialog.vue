<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth || '800px'"
    :close-on-click-modal="false" append-to-body destroy-on-close>
    <div class="form-container" :style="{ height: formOption.dialogHeight || 'auto' }">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" v-loading="formLoading">
        <!-- 基本信息 -->
        <div class="form-section">
          <h4 class="section-title">机构基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="培训订单" prop="orderId" v-if="dialogType !== 'view'">
                <el-select v-model="formData.orderId" placeholder="请选择培训订单" style="width: 100%"
                  :disabled="dialogType === 'edit'">
                  <el-option v-for="order in trainingOrders" :key="order.orderId" :label="order.orderTitle"
                    :value="order.orderId" />
                </el-select>
              </el-form-item>
              <el-form-item label="培训订单" v-else>
                <span>{{ formData.orderTitle || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构名称" prop="institutionName">
                <el-input v-model="formData.institutionName" placeholder="请输入机构名称" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="机构代码" prop="institutionCode">
                <el-input v-model="formData.institutionCode" placeholder="统一社会信用代码" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法定代表人" prop="legalPerson">
                <el-input v-model="formData.legalPerson" placeholder="请输入法定代表人" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="机构类型" prop="institutionType">
                <el-select v-model="formData.institutionType" placeholder="请选择机构类型" style="width: 100%"
                  :disabled="dialogType === 'view'">
                  <el-option label="企业" value="企业"></el-option>
                  <el-option label="事业单位" value="事业单位"></el-option>
                  <el-option label="社会组织" value="社会组织"></el-option>
                  <el-option label="其他" value="其他"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成立时间" prop="establishedDate">
                <el-date-picker v-model="formData.establishedDate" type="date" placeholder="选择成立时间" style="width: 100%"
                  :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="注册资本" prop="registeredCapital">
                <el-input-number v-model="formData.registeredCapital" :min="0" :precision="2" placeholder="万元"
                  style="width: 100%" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请状态" v-if="dialogType === 'view'">
                <el-tag :type="getStatusTagType(formData.applicationStatus)">
                  {{ getStatusText(formData.applicationStatus) }}
                </el-tag>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="机构地址" prop="institutionAddress">
            <el-input v-model="formData.institutionAddress" placeholder="请输入机构详细地址" :disabled="dialogType === 'view'" />
          </el-form-item>

          <el-form-item label="经营范围" prop="businessScope">
            <el-input v-model="formData.businessScope" type="textarea" :rows="3" placeholder="请输入经营范围"
              :disabled="dialogType === 'view'" />
          </el-form-item>
        </div>

        <!-- 联系信息 -->
        <div class="form-section">
          <h4 class="section-title">联系信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="联系人" prop="contactPerson">
                <el-input v-model="formData.contactPerson" placeholder="请输入联系人" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系邮箱" prop="contactEmail">
                <el-input v-model="formData.contactEmail" placeholder="请输入联系邮箱" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 培训能力 -->
        <div class="form-section">
          <h4 class="section-title">培训能力</h4>
          <el-form-item label="培训经验" prop="trainingExperience">
            <el-input v-model="formData.trainingExperience" type="textarea" :rows="4"
              placeholder="请详细描述您的培训经验，包括培训年限、培训领域、培训规模等" :disabled="dialogType === 'view'" />
          </el-form-item>

          <el-form-item label="培训能力" prop="trainingCapacity">
            <el-input v-model="formData.trainingCapacity" type="textarea" :rows="4"
              placeholder="请详细描述您的培训能力，包括培训体系、培训方法、培训效果等" :disabled="dialogType === 'view'" />
          </el-form-item>

          <el-form-item label="培训计划" prop="trainingPlan">
            <el-input v-model="formData.trainingPlan" type="textarea" :rows="4" placeholder="请详细描述针对此培训项目的具体培训计划"
              :disabled="dialogType === 'view'" />
          </el-form-item>

          <el-form-item label="师资信息" prop="teacherInfo">
            <el-input v-model="formData.teacherInfo" type="textarea" :rows="4" placeholder="请详细描述师资队伍情况，包括讲师数量、资质、经验等"
              :disabled="dialogType === 'view'" />
          </el-form-item>

          <el-form-item label="设施设备" prop="facilityInfo">
            <el-input v-model="formData.facilityInfo" type="textarea" :rows="3" placeholder="请描述培训场地、设备等硬件设施情况"
              :disabled="dialogType === 'view'" />
          </el-form-item>
        </div>

        <!-- 申请备注 -->
        <div class="form-section">
          <h4 class="section-title">申请信息</h4>
          <el-form-item label="申请备注" prop="applicationNote">
            <el-input v-model="formData.applicationNote" type="textarea" :rows="3" placeholder="请输入申请备注（选填）"
              :disabled="dialogType === 'view'" />
          </el-form-item>
        </div>

        <!-- 材料上传 -->
        <div class="form-section" v-if="dialogType !== 'view'">
          <h4 class="section-title">申请材料上传</h4>

          <div class="required-materials">
            <div class="material-item" v-for="(material, index) in requiredMaterials" :key="index"
                 :class="{ 'required-material': material.required, 'optional-material': !material.required }">
              <div class="material-header">
                <div class="material-info">
                  <div class="material-name">
                    <el-icon class="material-icon">
                      <Document />
                    </el-icon>
                    <span>{{ material.name }}</span>
                  </div>
                  <div class="material-status">
                    <el-tag v-if="material.required" type="danger" size="small">必需</el-tag>
                    <el-tag v-else type="success" size="small">可选</el-tag>
                  </div>
                </div>
              </div>

              <div class="material-upload">
                <FileUpload v-model:value="material.files" :limit="5" :file-size="10"
                  :file-type="['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']" :is-show-tip="true"
                  @fileLoad="(data) => handleFileLoad(data, index)" />
              </div>
            </div>
          </div>
        </div>

        <!-- 已上传材料查看 -->
        <div class="form-section" v-if="dialogType === 'view'">
          <h4 class="section-title">已上传材料</h4>

          <div class="uploaded-materials">
            <div class="material-item" v-for="(material, index) in requiredMaterials" :key="index">
              <div class="material-header">
                <div class="material-info">
                  <div class="material-name">
                    <el-icon class="material-icon">
                      <Document />
                    </el-icon>
                    <span>{{ material.name }}</span>
                  </div>
                  <div class="material-status">
                    <el-tag v-if="material.files && material.files.length > 0" type="success" size="small">
                      已上传 {{ material.files.length }} 个文件
                    </el-tag>
                    <el-tag v-else type="info" size="small">未上传</el-tag>
                  </div>
                </div>
              </div>

              <div class="material-files" v-if="material.files && material.files.length > 0">
                <div class="file-grid">
                  <div class="file-card" v-for="(file, fileIndex) in material.files" :key="fileIndex">
                    <FileView
                      :file="{ filePath: file.url || file.filePath, sourceFileName: file.name || file.fileName }" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 审核信息（仅查看时显示） -->
          <template v-if="dialogType === 'view' && formData.applicationStatus !== '0'">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="审核时间">
                  <span>{{ formatDateTime(formData.reviewTime) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审核人">
                  <span>{{ formData.reviewer || '--' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="审核意见" v-if="formData.reviewComment">
              <span>{{ formData.reviewComment }}</span>
            </el-form-item>
          </template>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ dialogType === 'view' ? '关闭' : '取消' }}</el-button>
        <el-button v-if="dialogType !== 'view'" type="primary" :loading="submitLoading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { listTrainingOrder } from '@/api/training/order'
import FileUpload from '@/components/FileUpload/index.vue'
import FileView from '@/components/FileView/index.vue'

const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('add') // add, edit, view
const formLoading = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const trainingOrders = ref([])

// 表单数据
const formData = reactive({
  applicationId: null,
  orderId: null,
  institutionName: '',
  institutionCode: '',
  legalPerson: '',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  institutionAddress: '',
  institutionType: '',
  establishedDate: null,
  registeredCapital: null,
  businessScope: '',
  trainingExperience: '',
  trainingCapacity: '',
  trainingPlan: '',
  teacherInfo: '',
  facilityInfo: '',
  applicationNote: '',
  applicationStatus: '0'
})

// 表单验证规则
const formRules = {
  orderId: [
    { required: true, message: '请选择培训订单', trigger: 'change' }
  ],
  institutionName: [
    { required: true, message: '请输入机构名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  legalPerson: [
    { required: true, message: '请输入法定代表人', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  institutionAddress: [
    { required: true, message: '请输入机构地址', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  trainingExperience: [
    { required: true, message: '请输入培训经验', trigger: 'blur' }
  ],
  trainingCapacity: [
    { required: true, message: '请输入培训能力', trigger: 'blur' }
  ],
  trainingPlan: [
    { required: true, message: '请输入培训计划', trigger: 'blur' }
  ],
  teacherInfo: [
    { required: true, message: '请输入师资信息', trigger: 'blur' }
  ]
}

// 所需材料列表
const requiredMaterials = ref([
  {
    name: '机构营业执照或组织机构代码证',
    required: true,
    files: [],
    field: 'qualificationFiles'
  },
  {
    name: '培训计划详细方案',
    required: false,
    files: [],
    field: 'trainingPlanFile'
  },
  {
    name: '师资队伍资质证明材料',
    required: false,
    files: [],
    field: 'teacherCertFiles'
  },
  {
    name: '培训场地及设施设备证明',
    required: false,
    files: [],
    field: 'facilityFiles'
  },
  {
    name: '其他相关资质证明材料',
    required: false,
    files: [],
    field: 'otherFiles'
  }
])

// 监听对话框显示状态
watch(dialogVisible, (newVal) => {
  if (newVal) {
    loadTrainingOrders()
  }
})

// 加载培训订单列表
const loadTrainingOrders = async () => {
  try {
    const response = await listTrainingOrder({ orderStatus: '1' })
    trainingOrders.value = response.rows || []
  } catch (error) {
    console.error('加载培训订单失败:', error)
  }
}

// 打开对话框
const openDialog = (type, title, data = {}) => {
  dialogType.value = type
  dialogTitle.value = title

  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (data[key] !== undefined) {
      formData[key] = data[key]
    } else {
      // 重置为默认值
      if (key === 'applicationStatus') {
        formData[key] = '0'
      } else if (typeof formData[key] === 'string') {
        formData[key] = ''
      } else if (typeof formData[key] === 'number') {
        formData[key] = null
      } else {
        formData[key] = null
      }
    }
  })

  // 处理文件数据
  requiredMaterials.value.forEach(material => {
    const fileData = data[material.field]
    if (fileData) {
      try {
        let parsedFiles = JSON.parse(fileData) || []
        // 确保文件对象包含所有必要字段
        material.files = parsedFiles.map(file => ({
          name: file.name || file.fileName || file.sourceFileName,
          fileName: file.fileName || file.name || file.sourceFileName,
          sourceFileName: file.sourceFileName || file.name || file.fileName,
          url: file.url || file.filePath,
          filePath: file.filePath || file.url,
          uid: file.uid || new Date().getTime() + Math.random()
        }))
      } catch (error) {
        material.files = []
      }
    } else {
      material.files = []
    }
  })

  dialogVisible.value = true

  // 清除表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 处理提交
const handleSubmit = () => {
  if (!formRef.value) return

  formRef.value.validate((valid) => {
    if (valid) {
      // 检查必需材料是否已上传（仅在新增和编辑时检查）
      if (dialogType.value !== 'view') {
        const requiredNotUploaded = requiredMaterials.value.filter(m => m.required && (!m.files || m.files.length === 0))
        if (requiredNotUploaded.length > 0) {
          ElMessage.error('请上传所有必需的材料文件')
          return
        }
      }

      submitLoading.value = true

      // 处理文件数据
      const formDataWithFiles = { ...formData }
      requiredMaterials.value.forEach(material => {
        if (material.files && material.files.length > 0) {
          formDataWithFiles[material.field] = JSON.stringify(material.files.map(file => ({
            name: file.name || file.fileName || file.sourceFileName,
            fileName: file.fileName || file.name || file.sourceFileName,
            sourceFileName: file.sourceFileName || file.name || file.fileName,
            url: file.url || file.filePath,
            filePath: file.filePath || file.url
          })))
        }
      })

      // 发送提交事件
      emit('submit', {
        type: dialogType.value,
        data: formDataWithFiles
      })
    }
  })
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 提交成功回调
const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
}

// 提交失败回调
const onSubmitError = () => {
  submitLoading.value = false
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 文件上传处理
const handleFileLoad = (data, index) => {
  requiredMaterials.value[index].files = data.fileList || []
}



// 暴露方法
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError
})
</script>

<style lang="scss" scoped>
.form-container {
  overflow-y: auto;
  padding: 10px;

  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .section-title {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      padding-bottom: 10px;
      border-bottom: 2px solid #667eea;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
    color: #4a5568;
  }

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-select .el-input__wrapper) {
    border-radius: 8px;
  }

  :deep(.el-textarea__inner) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-date-editor) {
    width: 100%;
  }

  :deep(.el-input-number) {
    width: 100%;
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;

  .el-button {
    border-radius: 8px;
    font-weight: 600;
    padding: 12px 24px;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// 材料上传样式
.required-materials,
.uploaded-materials {
  .material-item {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: rgba(102, 126, 234, 0.3);
    }

    // 必填材料样式
    &.required-material {
      border-left: 4px solid #f56c6c;
      background: rgba(254, 240, 240, 0.5);

      &:hover {
        border-color: rgba(245, 108, 108, 0.4);
        background: rgba(254, 240, 240, 0.8);
      }
    }

    // 可选材料样式
    &.optional-material {
      border-left: 4px solid #67c23a;
      background: rgba(240, 249, 235, 0.5);

      &:hover {
        border-color: rgba(103, 194, 58, 0.4);
        background: rgba(240, 249, 235, 0.8);
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .material-header {
      margin-bottom: 15px;

      .material-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .material-name {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #2d3748;
          font-size: 15px;

          .material-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 18px;
          }
        }

        .material-status {
          display: flex;
          gap: 8px;
          flex-shrink: 0;
        }
      }
    }

    .material-upload {
      width: 100%;
      padding-left: 26px; // 对齐图标

      .uploaded-files {
        margin-top: 16px;

        .file-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .file-card {
            background: rgba(248, 250, 252, 0.9);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 8px;
            padding: 8px;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 1);
              border-color: rgba(102, 126, 234, 0.4);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transform: translateY(-1px);
            }
          }
        }
      }
    }

    .material-files {
      padding-left: 26px; // 对齐图标

      .file-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-top: 12px;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        .file-card {
          background: rgba(248, 250, 252, 0.9);
          border: 1px solid rgba(226, 232, 240, 0.8);
          border-radius: 12px;
          padding: 12px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(102, 126, 234, 0.4);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}
</style>
